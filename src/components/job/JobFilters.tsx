import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { <PERSON>lider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  JobFiltersProps,
  DEFAULT_FILTERS,
  JOB_TYPE_OPTIONS,
  EXPERIENCE_LEVEL_OPTIONS,
  LOCATION_TYPE_OPTIONS,
  SALARY_RANGE
} from "@/types/jobFilters";

export function JobFilters({ filters, onFiltersChange }: JobFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  // Update local filters when props change
  useState(() => {
    setLocalFilters(filters);
  });

  const handleFilterChange = (key: keyof typeof filters, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
  };

  const handleResetFilters = () => {
    const resetFilters = { ...DEFAULT_FILTERS };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  return (
    <div className="py-4 space-y-6 overflow-y-auto max-h-[80vh]">
      <div className="space-y-6">
        <div className="space-y-3">
          <Label className="text-sm font-medium">Job Type</Label>
          <RadioGroup
            value={localFilters.jobType || 'all'}
            onValueChange={(value) => handleFilterChange('jobType', value)}
            className="space-y-2"
          >
            {JOB_TYPE_OPTIONS.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.value} />
                <Label htmlFor={option.value} className="text-sm">{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Experience Level</Label>
          <Select
            value={localFilters.experienceLevel || ''}
            onValueChange={(value) => handleFilterChange('experienceLevel', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Levels</SelectItem>
              {EXPERIENCE_LEVEL_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">
            Salary Range (${(localFilters.salaryRange?.[0] || SALARY_RANGE.DEFAULT_MIN).toLocaleString()} - ${(localFilters.salaryRange?.[1] || SALARY_RANGE.DEFAULT_MAX).toLocaleString()})
          </Label>
          <div className="px-2">
            <Slider
              min={SALARY_RANGE.MIN}
              max={SALARY_RANGE.MAX}
              step={SALARY_RANGE.STEP}
              value={localFilters.salaryRange || [SALARY_RANGE.DEFAULT_MIN, SALARY_RANGE.DEFAULT_MAX]}
              onValueChange={(value) => handleFilterChange('salaryRange', value as [number, number])}
              className="w-full"
            />
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Location</Label>
          <Select
            value={localFilters.location || ''}
            onValueChange={(value) => handleFilterChange('location', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Locations</SelectItem>
              {LOCATION_TYPE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="urgent" className="text-sm font-medium">Urgent Positions Only</Label>
            <Switch
              id="urgent"
              checked={localFilters.urgentOnly || false}
              onCheckedChange={(checked) => handleFilterChange('urgentOnly', checked)}
            />
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="featured" className="text-sm font-medium">Featured Jobs Only</Label>
            <Switch
              id="featured"
              checked={localFilters.featuredOnly || false}
              onCheckedChange={(checked) => handleFilterChange('featuredOnly', checked)}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
        <Button className="flex-1 w-full" onClick={handleApplyFilters}>
          Apply Filters
        </Button>
        <Button variant="outline" className="flex-1 w-full" onClick={handleResetFilters}>
          Reset
        </Button>
      </div>
    </div>
  );
}