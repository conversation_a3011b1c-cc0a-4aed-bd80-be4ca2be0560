import React, { useState, useMemo } from "react";
import { useJob<PERSON>, <PERSON> } from "@/hooks/useJobs";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";


import { matchCandidatesToJob } from "@/utils/ai";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Users, Star, Share2, MoreVertical, Briefcase, MapPin, Clock, DollarSign } from "lucide-react";
import { Dialog } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { JobMatchingModal } from "./JobMatchingModal";
import { JobDetailsDialog } from "./JobDetailsDialog";
import { EditJobDialog } from "./EditJobDialog";

import { useUpdateJob } from "@/hooks/useUpdateJob";
import { formatDistanceToNow } from "date-fns";
import { useAuth } from "@/contexts/AuthContext";
import { JobFilters as JobFiltersType } from "@/types/jobFilters";

interface JobMatch {
  id: string;
  name: string;
  match: number;
  role?: string;
  location?: string;
  experience?: string;
  skills?: string[];
  avatar?: string;
  email?: string;
}

interface JobListProps {
  filters?: JobFiltersType;
}

export const JobList = ({ filters }: JobListProps) => {
  const [matchingCandidates, setMatchingCandidates] = useState<JobMatch[]>([]);
  const [isMatching, setIsMatching] = useState(false);
  const [selectedJob, setSelectedJob] = useState<{ id: string; title: string } | null>(null);
  const [selectedJobDetails, setSelectedJobDetails] = useState<any>(null);
  const [editingJob, setEditingJob] = useState<Job | null>(null);
  
  
  const { user } = useAuth();
  const updateJobMutation = useUpdateJob();
  const { toast } = useToast();
  
  // Use unified jobs hook for real-time updates
  const { data: allJobs, isLoading: loading, error } = useJobs();

  // Filter jobs based on the provided filters
  const jobs = useMemo(() => {
    if (!allJobs || !filters) return allJobs || [];

    return allJobs.filter((job) => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchableText = [
          job.title,
          job.department,
          job.location,
          job.description,
          ...(job.requirements || [])
        ].join(' ').toLowerCase();

        if (!searchableText.includes(searchTerm)) {
          return false;
        }
      }

      // Job type filter
      if (filters.jobType && filters.jobType !== 'all' && job.job_type !== filters.jobType) {
        return false;
      }

      // Experience level filter (match against experience_required field)
      if (filters.experienceLevel && job.experience_required) {
        const experienceMap: Record<string, string[]> = {
          'entry': ['entry', 'junior', '0-2', '1-2', 'entry level'],
          'mid': ['mid', 'middle', '3-5', '2-5', 'mid level'],
          'senior': ['senior', '6-10', '5-10', 'senior level'],
          'lead': ['lead', 'principal', '10+', 'lead level', 'leadership']
        };

        const experienceText = job.experience_required.toLowerCase();
        const matchingTerms = experienceMap[filters.experienceLevel] || [];

        if (!matchingTerms.some(term => experienceText.includes(term))) {
          return false;
        }
      }

      // Salary range filter
      if (filters.salaryRange && job.salary_range) {
        const [minFilter, maxFilter] = filters.salaryRange;
        // Extract numbers from salary range string (e.g., "$50,000 - $80,000")
        const salaryNumbers = job.salary_range.match(/\d+/g);
        if (salaryNumbers && salaryNumbers.length >= 2) {
          const jobMinSalary = parseInt(salaryNumbers[0]) * (salaryNumbers[0].length <= 3 ? 1000 : 1);
          const jobMaxSalary = parseInt(salaryNumbers[1]) * (salaryNumbers[1].length <= 3 ? 1000 : 1);

          // Check if job salary range overlaps with filter range
          if (jobMaxSalary < minFilter || jobMinSalary > maxFilter) {
            return false;
          }
        }
      }

      // Location filter (match against location field)
      if (filters.location && job.location) {
        const locationText = job.location.toLowerCase();
        const filterLocation = filters.location.toLowerCase();

        if (!locationText.includes(filterLocation)) {
          return false;
        }
      }

      // Urgent filter
      if (filters.urgentOnly && !job.is_urgent) {
        return false;
      }

      // Featured filter (assuming featured jobs have higher applicant count or are urgent)
      if (filters.featuredOnly && !job.is_urgent && job.applicant_count < 10) {
        return false;
      }

      return true;
    });
  }, [allJobs, filters]);

  const handleFindMatches = async (jobId: string, jobTitle: string) => {
    setIsMatching(true);
    setSelectedJob({ id: jobId, title: jobTitle });
    try {
      const matches = await matchCandidatesToJob(jobId);
      setMatchingCandidates(matches);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to find matches. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsMatching(false);
    }
  };

  const handleSaveJob = (updatedJob: Job) => {
    updateJobMutation.mutate(updatedJob);
    setEditingJob(null);
  };

  const handleArchiveJob = (jobId: string) => {
    updateJobMutation.mutate({ id: jobId, is_active: false });
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      {jobs.map((job) => (
        <Card key={job.id} onClick={() => setSelectedJobDetails(job)} className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <h3 className="font-semibold text-lg">{job.title}</h3>
                  <Badge variant={job.is_active ? 'default' : 'secondary'}>{job.is_active ? 'Open' : 'Archived'}</Badge>
                </div>
                <p className="text-sm text-muted-foreground">{job.department}</p>
                <div className="mt-2 flex flex-wrap gap-x-4 gap-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Briefcase className="w-4 h-4" />
                    <span>{job.job_type}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4" />
                    <span>{job.location}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <Clock className="w-4 h-4" />
                    <span>Posted {formatDistanceToNow(new Date(job.created_at), { addSuffix: true })}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <DollarSign className="w-4 h-4" />
                    <span>{job.salary_range}</span>
                  </div>
                </div>
              </div>
              <div className="flex sm:flex-col justify-between items-end gap-2">
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <Star className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Add to Favorites</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Share Job</TooltipContent>
                  </Tooltip>
                  <div onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => setEditingJob(job)}>
                          Edit Job
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleArchiveJob(job.id)}>
                          Archive Job
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3" onClick={(e) => e.stopPropagation()}>
              <div className="text-sm text-muted-foreground">
                {job.applicant_count} applicants
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleFindMatches(job.id, job.title)}
                disabled={isMatching}
                className="w-full sm:w-auto"
              >
                {isMatching ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Users className="w-4 h-4 mr-2" />
                )}
                <span className="hidden sm:inline">Find Matches</span>
                <span className="sm:hidden">Matches</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {selectedJob && (
        <JobMatchingModal
          isOpen={matchingCandidates.length > 0}
          onClose={() => {
            setMatchingCandidates([]);
            setSelectedJob(null);
          }}
          matches={matchingCandidates}
          jobTitle={selectedJob.title}
        />
      )}

      {selectedJobDetails && (
        <Dialog open={!!selectedJobDetails} onOpenChange={() => setSelectedJobDetails(null)}>
          <JobDetailsDialog
            job={selectedJobDetails}
            onClose={() => setSelectedJobDetails(null)}
          />
        </Dialog>
      )}

      {editingJob && (
        <EditJobDialog
          job={editingJob}
          isOpen={!!editingJob}
          onClose={() => setEditingJob(null)}
          onSave={handleSaveJob}
        />
      )}
    </div>
  );
};