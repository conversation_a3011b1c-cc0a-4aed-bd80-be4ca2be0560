
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { JobPostingForm } from "@/components/job/JobPostingForm";
import { Search, SlidersHorizontal, Briefcase } from "lucide-react";
import { JobList } from "@/components/job/JobList";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { JobFilters } from "@/components/job/JobFilters";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { JobAnalytics } from "@/components/job/JobAnalytics";
import { JobP<PERSON>eline } from "@/components/job/JobPipeline";
import { JobIntegrations } from "@/components/job/JobIntegrations";
import { JobFilters as JobFiltersType, DEFAULT_FILTERS } from "@/types/jobFilters";
import { useDebounce } from "@/hooks/useDebounce";

const Jobs = () => {
  const [isPostDialogOpen, setIsPostDialogOpen] = useState(false);
  const [filters, setFilters] = useState<JobFiltersType>(DEFAULT_FILTERS);
  const [searchQuery, setSearchQuery] = useState("");

  // Debounce search query for better performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Update filters when debounced search query changes
  useEffect(() => {
    setFilters(prev => ({ ...prev, search: debouncedSearchQuery }));
  }, [debouncedSearchQuery]);

  // Handle filter changes
  const handleFiltersChange = (newFilters: JobFiltersType) => {
    setFilters(newFilters);
    // Update search query if it changed via filters
    if (newFilters.search !== searchQuery) {
      setSearchQuery(newFilters.search || "");
    }
  };

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };
  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-0">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div className="flex items-center gap-2 min-w-0">
          <Briefcase className="w-6 h-6 sm:w-8 sm:h-8 text-primary flex-shrink-0" />
          <h1 className="text-2xl sm:text-3xl font-bold truncate">Jobs</h1>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <SlidersHorizontal className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Jobs</SheetTitle>
              </SheetHeader>
              <JobFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
            </SheetContent>
          </Sheet>
            <Dialog open={isPostDialogOpen} onOpenChange={setIsPostDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full sm:w-auto">
                  <Plus className="w-4 h-4 mr-2" />
                  Post Job
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Post a Job</DialogTitle>
                </DialogHeader>
                <JobPostingForm onSuccess={() => setIsPostDialogOpen(false)} />
              </DialogContent>
            </Dialog>
        </div>
      </div>

      <Tabs defaultValue="active" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto">
          <TabsTrigger value="active" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Active </span>Jobs
          </TabsTrigger>
          <TabsTrigger value="pipeline" className="text-xs sm:text-sm">Pipeline</TabsTrigger>
          <TabsTrigger value="analytics" className="text-xs sm:text-sm">Analytics</TabsTrigger>
          <TabsTrigger value="integrations" className="text-xs sm:text-sm">
            <span className="hidden sm:inline">Integration</span>
            <span className="sm:hidden">Int</span>s
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4 min-w-0">
          <Card className="min-w-0">
            <CardHeader className="space-y-3 sm:space-y-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg sm:text-xl">Active Jobs</CardTitle>
              </div>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search jobs..."
                  className="pl-8 w-full"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
            </CardHeader>
            <CardContent className="min-w-0">
              <JobList filters={filters} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pipeline" className="min-w-0">
          <JobPipeline />
        </TabsContent>

        <TabsContent value="analytics" className="min-w-0">
          <JobAnalytics />
        </TabsContent>
        
        <TabsContent value="integrations" className="min-w-0">
          <JobIntegrations />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Jobs;
